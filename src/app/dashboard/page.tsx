import Image from "next/image";
import Link from "next/link";
import { redirect } from "next/navigation";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import ChangeBackground from "@/components/ChangeBackground";
import { formatVietnamDateTime } from "@/lib/utils";
import { createDrizzle } from "@/config/database";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Heart, Users, UserPlus } from "lucide-react";

export default async function DashboardPage() {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) redirect("/auth/signin");

  const { data: profile } = await supabase.from("profiles").select("background_url").eq("id", user.id).maybeSingle();
  const { data: recent } = await supabase
    .from("checkins")
    .select("id, photo_url, created_at")
    .eq("user_id", user.id)
    .order("created_at", { ascending: false })
    .limit(6);

  // Ensure user profile exists with email
  const { client: syncClient } = createDrizzle();
  await syncClient.connect();
  try {
    await syncClient.query(
      `INSERT INTO profiles (id, email, display_name) VALUES ($1, $2, $3)
       ON CONFLICT (id) DO UPDATE SET
       email = COALESCE(profiles.email, $2),
       display_name = COALESCE(profiles.display_name, $3)`,
      [user.id, user.email, user.email?.split('@')[0] || 'User']
    );
  } catch (error) {
    console.error("Profile sync error:", error);
  } finally {
    await syncClient.end();
  }

  // Get social activity data
  const { client } = createDrizzle();
  await client.connect();

  try {
    // Get recent social feed items (2 most recent)
    const { rows: socialFeed } = await client.query(
      `SELECT
        c.id,
        c.photo_url,
        c.created_at,
        p.display_name,
        p.email
       FROM checkins c
       JOIN follows f ON c.user_id = f.following_id
       JOIN profiles p ON c.user_id = p.id
       WHERE f.follower_id = $1
       ORDER BY c.created_at DESC
       LIMIT 2`,
      [user.id]
    );

    // Get following count
    const { rows: followingCountRows } = await client.query(
      `SELECT COUNT(*)::int as count FROM follows WHERE follower_id = $1`,
      [user.id]
    );

    const followingCount = followingCountRows[0]?.count || 0;

    return (
      <main className="min-h-dvh">
        <section className="relative h-56 w-full overflow-hidden rounded-b-xl">
          {profile?.background_url ? (
            <Image src={profile.background_url} alt="Background" fill className="object-cover" priority />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-muted text-muted-foreground">No background</div>
          )}
          <div className="absolute inset-0 bg-black/30" />
          <div className="absolute inset-x-0 bottom-4 flex items-center justify-between px-4">
            <h1 className="text-xl font-semibold text-white">Welcome</h1>
            <ChangeBackground />
          </div>
        </section>

        <section className="mx-auto max-w-2xl px-4 py-6 space-y-6">
          {/* Social Activity Preview */}
          {followingCount > 0 && (
            <Card className="overflow-hidden bg-gradient-to-r from-pink-100 to-purple-100 dark:from-pink-900/20 dark:to-purple-900/20 border-pink-200 dark:border-pink-800">
              <CardHeader className="flex flex-row items-center justify-between pb-3">
                <div>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Heart className="h-5 w-5 text-pink-600" />
                    Social Activity
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Following {followingCount} people
                  </p>
                </div>
                <Button asChild size="sm" className="rounded-full">
                  <Link href="/social">
                    View Feed
                  </Link>
                </Button>
              </CardHeader>
              {socialFeed.length > 0 && (
                <CardContent className="pt-0">
                  <div className="flex gap-2">
                    {socialFeed.map((item) => (
                      <div key={item.id} className="relative w-16 h-16 rounded-lg overflow-hidden">
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img
                          src={item.photo_url}
                          alt="Recent activity"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))}
                    <div className="flex-1 flex items-center text-sm text-muted-foreground">
                      Recent check-ins from people you follow
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          )}

          {/* Discover People CTA */}
          {followingCount === 0 && (
            <Card className="overflow-hidden bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20 border-blue-200 dark:border-blue-800">
              <CardContent className="p-6 text-center">
                <Users className="h-12 w-12 mx-auto mb-4 text-blue-600" />
                <h3 className="text-lg font-medium mb-2">Discover People</h3>
                <p className="text-muted-foreground mb-4">
                  Find and follow other users to see their check-ins in your social feed!
                </p>
                <Button asChild className="rounded-full">
                  <Link href="/social/discover" className="flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    Find People
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Recent Check-ins */}
          <div>
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-lg font-medium">Recent Check-ins</h2>
              <Link href="/history" className="text-sm text-primary underline">
                View all
              </Link>
            </div>
            {recent && recent.length > 0 ? (
              <div className="grid grid-cols-2 gap-3 sm:grid-cols-3">
                {recent.map((c) => (
                  <Link
                    key={c.id}
                    href={`/history/${c.id}`}
                    className="group relative block aspect-square overflow-hidden rounded-md animate-in fade-in-0 slide-in-from-bottom-1 duration-300"
                  >
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={c.photo_url}
                      alt="Check-in"
                      className="h-full w-full object-cover transition-transform group-hover:scale-105"
                    />
                    <div className="absolute bottom-0 w-full bg-black/40 p-1 text-[11px] text-white">
                      {formatVietnamDateTime(c.created_at)}
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No check-ins yet.</p>
            )}
          </div>
        </section>
      </main>
    );
  } catch (error) {
    console.error("Dashboard error:", error);
    await client.end();
    return (
      <main className="min-h-dvh">
        <section className="relative h-56 w-full overflow-hidden rounded-b-xl">
          <div className="flex h-full w-full items-center justify-center bg-muted text-muted-foreground">No background</div>
          <div className="absolute inset-0 bg-black/30" />
          <div className="absolute inset-x-0 bottom-4 flex items-center justify-between px-4">
            <h1 className="text-xl font-semibold text-white">Welcome</h1>
            <ChangeBackground />
          </div>
        </section>
        <section className="mx-auto max-w-2xl px-4 py-6">
          <p className="text-muted-foreground">Something went wrong. Please try again later.</p>
        </section>
      </main>
    );
  }
}

