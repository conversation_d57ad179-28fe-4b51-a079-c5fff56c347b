import { NextRequest, NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { createDrizzle } from "@/config/database";

export async function GET(req: NextRequest) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(req.url);
  const email = searchParams.get("email");
  
  if (!email || email.length < 3) {
    return NextResponse.json({ error: "Email query must be at least 3 characters" }, { status: 400 });
  }

  const { client } = createDrizzle();
  await client.connect();
  
  try {
    // Search through profiles by email
    const { rows } = await client.query(
      `SELECT id, email, display_name
       FROM profiles
       WHERE email ILIKE $1 AND id != $2
       ORDER BY email
       LIMIT 10`,
      [`%${email}%`, user.id]
    );

    const results = rows.map(row => ({
      id: row.id,
      email: row.email,
      displayName: row.display_name || row.email?.split('@')[0] || 'Unknown User',
    }));

    return NextResponse.json({ users: results });
  } catch (error) {
    console.error("Search error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  } finally {
    await client.end();
  }
}
